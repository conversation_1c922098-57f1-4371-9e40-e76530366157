# Real-Time Conversation System

This directory contains the implementation of a real-time voice conversation system that enables natural, interruptible conversations with AI agents.

## 🎯 Overview

The real-time conversation system transforms the original single-turn workflow into a continuous, interactive conversation where:

- **AI speaks while monitoring for user interruptions**
- **Users can interrupt AI responses at any time**
- **Conversation continues naturally after interruptions**
- **Different interrupt behaviors for reversible vs irreversible actions**

## 📁 Files

### Core Implementation
- `test_audio_interrupt_orchestrator.py` - Main real-time conversation test
- `demo_realtime_conversation.py` - Concept demonstration (no hardware required)

### Configuration
- `../../workflows/banking_workflow_v2.json` - Updated workflow with interrupt support
- `../../agents/orchestrator_agent_v3.py` - Enhanced orchestrator with continuous mode

## 🚀 Quick Start

### 1. Run the Demo (No Hardware Required)
```bash
python tests/e2e/demo_realtime_conversation.py
```
This shows the conversation concept without requiring microphone/speakers.

### 2. Run the Real-Time Test (Requires Hardware)
```bash
python tests/e2e/test_audio_interrupt_orchestrator.py
```
This runs the actual real-time conversation with microphone input.

## 🏗️ Architecture

### Key Components

1. **RealTimeConversationManager**
   - Manages continuous conversation loop
   - Coordinates interrupt detection
   - Handles conversation state

2. **Enhanced Orchestrator**
   - `continuous_mode` - Enables persistent conversation
   - `conversation_active` - Controls conversation lifecycle
   - Dynamic state transitions back to "Inquiry"

3. **Interrupt System**
   - Real-time microphone monitoring
   - VAD (Voice Activity Detection)
   - Context-aware interrupt handling

4. **Workflow Updates**
   - Interrupts enabled globally
   - Return transitions to "Inquiry" state
   - Reversible vs irreversible action configuration

### Conversation Flow

```
┌─────────────────┐
│   Greeting      │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐    ┌─────────────────┐
│   Inquiry       │◄───┤  Process User   │
│ (Listen/Wait)   │    │     Input       │
└─────────┬───────┘    └─────────────────┘
          │                      ▲
          ▼                      │
┌─────────────────┐              │
│  Execute Task   │──────────────┘
│ (Balance/Transfer)
└─────────────────┘
```

## 🎤 Interrupt Handling

### Interrupt Types

1. **Reversible Actions** (Balance Check, Information)
   - AI pauses immediately
   - Acknowledges: "Let me finish this first, then I'll respond"
   - Starts new flow after completion

2. **Irreversible Actions** (Money Transfer)
   - AI completes action first
   - Acknowledges: "The action has been completed. If something went wrong, let me know"
   - No queuing of interrupted input

### Interrupt Detection

- **VAD Threshold**: 0.05 (sensitive detection)
- **Method**: WebRTC VAD (production-ready)
- **Cooldown**: 0.0 seconds (immediate interrupts)
- **Confirmation**: 0.5 second window

## 🔧 Configuration

### Environment Variables
```bash
VAD_THRESHOLD=0.05
VAD_METHOD=webrtcvad
WEBRTC_AGGRESSIVENESS=3
TTS_INTERRUPT_COOLDOWN_SECONDS=0.0
SIMPLE_INTERRUPT_CONFIRMATION=true
```

### Workflow Configuration
```json
{
  "interrupt_config": {
    "global_settings": {
      "enabled": true,
      "vad_threshold": 0.05,
      "tts_interrupt_cooldown_seconds": 0
    }
  }
}
```

## 🎭 Usage Examples

### Basic Conversation
```python
# Initialize system
conversation_manager = RealTimeConversationManager(...)
orchestrator.enable_continuous_mode()

# Start conversation
await conversation_manager.start_conversation()
```

### Interrupt Handling
```python
# During TTS playback
if interrupt_detected:
    await tts_monitor.pause_playback()
    user_input = await capture_interrupt_audio()
    await process_interrupt(user_input)
```

## 🧪 Testing

### Test Scenarios

1. **Basic Conversation**
   - Greeting → User request → AI response → Continue

2. **Interrupt During Information**
   - AI explaining balance → User interrupts → New request

3. **Interrupt During Transaction**
   - AI processing transfer → User tries to cancel → Completion handling

4. **Natural Ending**
   - User says "goodbye" → AI responds → Conversation ends

### Expected Behavior

- ✅ Continuous conversation until explicit goodbye
- ✅ Real-time interrupt detection during TTS
- ✅ Context preservation across turns
- ✅ Appropriate handling of reversible/irreversible actions
- ✅ Natural conversation flow

## 🔍 Debugging

### Common Issues

1. **No Interrupt Detection**
   - Check microphone permissions
   - Verify VAD threshold settings
   - Ensure WebRTC VAD is installed

2. **Conversation Doesn't Continue**
   - Verify `continuous_mode` is enabled
   - Check workflow transitions back to "Inquiry"
   - Ensure `conversation_active` flag

3. **Audio Issues**
   - Test microphone with `select_input_device()`
   - Check audio file paths in memory
   - Verify TTS playback system

### Debug Logs
```python
logger = get_module_logger("realtime_conversation_test")
logger.info("Conversation turn started")
```

## 🚀 Future Enhancements

- **Multi-language support**
- **Emotion detection in interrupts**
- **Advanced VAD with noise filtering**
- **Conversation analytics and metrics**
- **Integration with external TTS/STT services**

## 📞 Support

For issues or questions about the real-time conversation system:
1. Run the demo first to understand concepts
2. Check debug logs for specific errors
3. Verify hardware setup (microphone/speakers)
4. Review configuration settings
