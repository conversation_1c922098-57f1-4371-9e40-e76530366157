#!/usr/bin/env python3
"""
Real-Time Conversation Demo <PERSON>ript

This script demonstrates the key concepts of the real-time conversation system
without requiring full hardware setup. It shows:

1. Continuous conversation loop
2. Interrupt detection simulation
3. Dynamic state transitions
4. Natural conversation flow

Run this to understand how the real-time system works before testing with actual hardware.
"""

import asyncio
import os
import sys
from datetime import datetime

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

class ConversationSimulator:
    """Simulates a real-time conversation with interrupt capabilities"""
    
    def __init__(self):
        self.conversation_active = True
        self.current_turn = 0
        self.interrupt_count = 0
        
    async def simulate_conversation(self):
        """Simulate a full conversation with interrupts"""
        print("🎭 Real-Time Conversation Simulation")
        print("=" * 50)
        print("This simulates how the real system would work:")
        print()
        
        # Conversation scenarios
        scenarios = [
            {
                "user_input": "Check my account balance",
                "ai_response": "Let me check your account balance for you...",
                "interrupt_point": 2.0,  # Interrupt after 2 seconds
                "interrupt_input": "Actually, I want to transfer money instead",
                "interrupt_response": "I understand. Let me help you with a transfer instead."
            },
            {
                "user_input": "What's the exchange rate for USD to EUR?",
                "ai_response": "The current exchange rate for USD to EUR is 0.85...",
                "interrupt_point": None,  # No interrupt
                "interrupt_input": None,
                "interrupt_response": None
            },
            {
                "user_input": "Transfer $500 to my savings account",
                "ai_response": "I'm processing your transfer of $500 to savings. This cannot be undone...",
                "interrupt_point": 3.0,  # Try to interrupt irreversible action
                "interrupt_input": "Wait, cancel that!",
                "interrupt_response": "The transfer has already been completed. If something went wrong, let me know and I'll help fix it."
            }
        ]
        
        for i, scenario in enumerate(scenarios, 1):
            await self._simulate_turn(i, scenario)
            
        # End conversation
        await self._simulate_goodbye()
        
        print("\n🎉 Conversation simulation completed!")
        print(f"📊 Statistics:")
        print(f"   - Total turns: {self.current_turn}")
        print(f"   - Interrupts handled: {self.interrupt_count}")
        print(f"   - Conversation duration: ~{self.current_turn * 5} seconds")
        
    async def _simulate_turn(self, turn_num, scenario):
        """Simulate a single conversation turn"""
        print(f"\n🔄 [TURN {turn_num}] Starting conversation turn")
        
        # User speaks
        print(f"👤 User: \"{scenario['user_input']}\"")
        await asyncio.sleep(1)
        
        # AI starts responding
        print(f"🤖 AI: \"{scenario['ai_response']}\"")
        print("   🔊 [TTS PLAYING] - AI is speaking...")
        
        # Check for interrupt
        if scenario['interrupt_point']:
            # Simulate TTS playing for a bit
            await asyncio.sleep(scenario['interrupt_point'])
            
            # User interrupts
            print(f"   ⚡ [INTERRUPT DETECTED] User started speaking!")
            print(f"   ⏸️  [TTS PAUSED] AI stops speaking")
            print(f"👤 User: \"{scenario['interrupt_input']}\"")
            
            self.interrupt_count += 1
            
            # AI handles interrupt
            await asyncio.sleep(0.5)
            print(f"   🧠 [PROCESSING] Analyzing interrupt...")
            print(f"🤖 AI: \"{scenario['interrupt_response']}\"")
            print("   🔊 [TTS RESUMED] AI responds to interrupt")
        else:
            # No interrupt, let AI finish
            await asyncio.sleep(3)
            print("   ✅ [TTS COMPLETED] AI finished speaking")
            
        self.current_turn += 1
        await asyncio.sleep(1)
        
    async def _simulate_goodbye(self):
        """Simulate conversation ending"""
        print(f"\n🔄 [TURN {self.current_turn + 1}] Final turn")
        print("👤 User: \"Thank you, goodbye\"")
        await asyncio.sleep(1)
        
        print("🤖 AI: \"Thank you for using our service. Goodbye!\"")
        print("   🔊 [TTS PLAYING] - Final response...")
        await asyncio.sleep(2)
        print("   ✅ [TTS COMPLETED] Conversation ended")
        
        self.conversation_active = False

async def demonstrate_interrupt_types():
    """Demonstrate different types of interrupts"""
    print("\n🎯 Interrupt Types Demonstration")
    print("=" * 40)
    
    interrupt_types = [
        {
            "name": "Reversible Action Interrupt",
            "description": "User interrupts during balance check (reversible)",
            "behavior": "AI pauses, acknowledges, starts new flow immediately"
        },
        {
            "name": "Irreversible Action Interrupt", 
            "description": "User interrupts during money transfer (irreversible)",
            "behavior": "AI completes action first, then acknowledges interrupt"
        },
        {
            "name": "Information Request Interrupt",
            "description": "User interrupts during information delivery",
            "behavior": "AI pauses, acknowledges, can resume or start new flow"
        }
    ]
    
    for i, interrupt_type in enumerate(interrupt_types, 1):
        print(f"\n{i}. {interrupt_type['name']}")
        print(f"   📝 {interrupt_type['description']}")
        print(f"   ⚙️  {interrupt_type['behavior']}")
        await asyncio.sleep(1)

async def show_system_architecture():
    """Show the system architecture for real-time conversation"""
    print("\n🏗️  Real-Time Conversation Architecture")
    print("=" * 45)
    
    components = [
        "🎤 Microphone Monitor - Continuously listens for voice",
        "🧠 VAD (Voice Activity Detection) - Detects when user speaks", 
        "⚡ Interrupt Manager - Handles interrupt events",
        "🔊 TTS Monitor - Controls AI speech playback",
        "🔄 State Manager - Manages conversation flow",
        "💭 Memory Manager - Tracks conversation context",
        "🎭 Orchestrator - Coordinates all components"
    ]
    
    for component in components:
        print(f"   {component}")
        await asyncio.sleep(0.5)
        
    print("\n🔗 Flow:")
    print("   1. AI speaks (TTS) while monitoring microphone")
    print("   2. VAD detects user voice → Interrupt triggered")
    print("   3. TTS pauses, user input captured & transcribed")
    print("   4. Intent analyzed, appropriate response generated")
    print("   5. AI responds, conversation continues")

async def main():
    """Main demo function"""
    print("🚀 Real-Time Conversation System Demo")
    print("=" * 50)
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Show architecture
    await show_system_architecture()
    
    print("\n" + "="*60)
    input("Press Enter to see interrupt types...")
    
    # Show interrupt types
    await demonstrate_interrupt_types()
    
    print("\n" + "="*60)
    input("Press Enter to start conversation simulation...")
    
    # Run conversation simulation
    simulator = ConversationSimulator()
    await simulator.simulate_conversation()
    
    print("\n" + "="*60)
    print("🎯 Key Features Demonstrated:")
    print("   ✅ Continuous conversation loop")
    print("   ✅ Real-time interrupt detection")
    print("   ✅ Context-aware responses")
    print("   ✅ Reversible vs irreversible action handling")
    print("   ✅ Natural conversation flow")
    print()
    print("🔧 To run the actual system:")
    print("   python tests/e2e/test_audio_interrupt_orchestrator.py")

if __name__ == "__main__":
    asyncio.run(main())
