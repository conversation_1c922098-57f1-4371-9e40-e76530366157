import asyncio
import json
import os
from random import randrange
from typing import Dict, Any, Optional, List
from core.memory.memory_manager import MemoryManager
from core.memory.redis_context import RedisClient
from core.logging.logger_config import get_module_logger
import openai

from core.state_manager.state_manager import StateManager

class OrchestratorV3:
    def __init__(self, session_id: str, workflow_name: str, state_manager : StateManager , memory_manager : MemoryManager, redis_client: RedisClient):
        self.session_id = session_id
        self.workflow_name = workflow_name
        self.state_manager : StateManager = state_manager
        self.memory_manager : MemoryManager = memory_manager
        self.redis_client = redis_client
        self.logger = get_module_logger("orchestrator_v3", session_id=session_id)
        self.prohibited_actions = None
        self.agent_response_cache: Dict[str, List[Dict[str, Any]]] = {}  # state_id -> list of responses
        self.workflow_summary = None
        self.status = "initialized"
        self.reason = None
        self.key_events = []

        # Track conversation for dialog logging
        self.user_message = ""
        self.ai_response = ""

        # Real-time conversation mode
        self.continuous_mode = False
        self.conversation_active = True

        # self.stt_input_list = [
        #     {"audio_path": "fillerWords/test_input_exchange_rate_1.mp3"},
        #     # {"audio_path": "fillerWords/user_conversation_part_1.mp3"},
        #     # {"audio_path": "fillerWords/test_input_balance_check_1.mp3"},
        #     {"audio_path": "fillerWords/test_input_transfer_funds_1.mp3"},
        #     {"audio_path": "fillerWords/test_input_3.mp3"}
        # ]
        # self.stt_input_list_index = 0

    async def initialize(self):
        # Cache prohibited actions at start
        if self.prohibited_actions is None:
            self.prohibited_actions = await self.state_manager.getProhibitedActions()
        # Preload agent responses for this session
        await self._populate_agent_response_cache()
        self.logger.info("OrchestratorV3 initialized", action="initialize", layer="orchestrator_v3", step="init")

    async def _populate_agent_response_cache(self):
        """Parse the conversations log and cache agent responses for this session."""
        log_path = os.path.join("logs", "conversations", "conversations.jsonl")
        if not os.path.exists(log_path):
            return
        self.agent_response_cache.clear()
        try:
            with open(log_path, "r", encoding="utf-8") as f:
                for line in f:
                    try:
                        entry = json.loads(line)
                        if entry.get("session_id") == self.session_id:
                            state_id = entry.get("state_id", "unknown")
                            if state_id not in self.agent_response_cache:
                                self.agent_response_cache[state_id] = []
                            self.agent_response_cache[state_id].append(entry)
                    except Exception:
                        continue
        except Exception as e:
            self.logger.error(f"Failed to populate agent response cache: {e}", action="populate_agent_response_cache", layer="orchestrator_v3")

    async def get_user_query(self, retries=3) -> Optional[str]:
        for attempt in range(retries):
            clean_text = await self.memory_manager.get("clean_text")
            if clean_text:
                return clean_text
            await asyncio.sleep(0.5)
        self.logger.error("User query (clean_text) not found after retries", action="get_user_query", layer="orchestrator_v3")
        return None

    async def get_agent_responses(self, agent_name: str) -> List[str]:
        # Return all agent outputs for this state from the cache
        responses = []
        for entry in self.agent_response_cache.get(agent_name, []):
            output = entry.get("output")
            if output:
                if isinstance(output, dict) and "llm_answer" in output:
                    responses.append(output["llm_answer"])
                elif isinstance(output, str):
                    responses.append(output)
        return responses

    async def get_agent_confidence(self, agent_name: str) -> Optional[float]:
        # Try to get confidence from Redis (publish/subscribe or key)
        try:
            key = f"session:{self.session_id}:agent:{agent_name}:confidence"
            confidence = await self.redis_client.get(key)
            if confidence is not None:
                try:
                    return float(confidence)
                except Exception:
                    return None
            self.logger.warning(f"Confidence value missing for agent {agent_name}", action="get_agent_confidence", layer="orchestrator_v3")
        except Exception as e:
            self.logger.warning(f"Error retrieving confidence for agent {agent_name}: {e}", action="get_agent_confidence", layer="orchestrator_v3")
        return None

    async def get_state_summary(self) -> str:
        # Get workflow and pipeline state summaries
        workflow_state = await self.state_manager.getCurrentWorkflowState()
        pipeline_state = await self.state_manager.getCurrentPipelineState()
        allowed_actions = await self.state_manager.getAllowedActions()

        summary = (
            f"Workflow: {self.workflow_name}\n"
            f"Current Workflow State: {workflow_state}\n"
            f"Current Pipeline State: {pipeline_state}\n"
            f"Allowed Actions: {allowed_actions}\n"
            f"Prohibited Actions: {self.prohibited_actions}"
        )
        return summary

    async def evaluate_with_llm(self, user_query: str, agent_responses: List[str], agent_confidence: Optional[float], state_summary: str) -> str:
        prompt = (
            "You are an AI orchestrator evaluating a conversation state. Based on the following information, decide whether to PROCEED to the next state or REDO the current state.\n"
            f"State Summary: {state_summary}\n"
            f"User Query: {user_query if user_query is not None else '[Not available]'}\n"
            f"Agent Responses: {agent_responses}\n"
            f"Agent Confidence: {agent_confidence}\n"
            "If the user query is not available, make your best decision based on the other information.\n"
            "Respond with ONLY one word: 'proceed' or 'redo'."
        )
        print("[DEBUG] LLM prompt:", prompt)
        print("[DEBUG] LLM inputs:", {
            "user_query": user_query,
            "agent_responses": agent_responses,
            "agent_confidence": agent_confidence,
            "state_summary": state_summary
        })
        try:
            client = openai.AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))
            response = await client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=10,
                temperature=0.1
            )
            decision = response.choices[0].message.content.strip().lower()
            print("[DEBUG] LLM raw response:", response)
            return "proceed" if "proceed" in decision else "redo"
        except Exception as e:
            print(f"[DEBUG] LLM evaluation failed, defaulting to 'redo': {e}")
            self.logger.error(f"LLM evaluation failed: {e}", action="evaluate_with_llm", layer="orchestrator_v3")
            return "redo"

    def enable_continuous_mode(self):
        """Enable continuous conversation mode"""
        self.continuous_mode = True
        self.conversation_active = True
        self.logger.info("Continuous conversation mode enabled", action="enable_continuous_mode", layer="orchestrator_v3")

    def stop_conversation(self):
        """Stop the continuous conversation"""
        self.conversation_active = False
        self.logger.info("Conversation stopped", action="stop_conversation", layer="orchestrator_v3")

    async def run(self):
        await self.initialize()
        self.status = "running"
        try:
            # In continuous mode, keep conversation active until explicitly stopped
            while self.conversation_active:

                # Always re-fetch the current pipeline state and pipeline at the start of each iteration
                state_summary = await self.get_state_summary()
                pipeline_state = await self.state_manager.getCurrentPipelineState()
                raw_pipeline = await self.state_manager.getCurrentPipeline()

                if hasattr(raw_pipeline, 'pipeline'):
                    current_pipeline = raw_pipeline.pipeline

                else:
                    current_pipeline = raw_pipeline

                if current_pipeline is None or not hasattr(current_pipeline, '__iter__'):
                    self.logger.error("Current pipeline is None or not iterable. Aborting.")
                    return {'status': 'aborted', 'reason': 'Current pipeline is None or not iterable.'}
                if pipeline_state is None:
                    self.logger.error("Pipeline state is None. Aborting.")
                    return {'status': 'aborted', 'reason': 'Pipeline state is None.'}
                current_step = None
                for step in current_pipeline:
                    if step is None:
                        self.logger.warning("Encountered None in current_pipeline steps. Skipping.")
                        continue
                    if hasattr(step, "step") and step.step == pipeline_state:
                        current_step = step
                        break
                if not current_step:
                    self.logger.error(f"No pipeline step found for state: {pipeline_state}. Aborting.")
                    return {'status': 'aborted', 'reason': f'No pipeline step found for state: {pipeline_state}'}
                agent_names = [current_step.agent] if isinstance(current_step.agent, str) else list(current_step.agent)

                # Get current workflow state to determine context
                workflow_state = await self.state_manager.getCurrentWorkflowState()
                
                # Handle special input cases - set text in memory for greeting/goodbye TTS
                input_data = {}
                if workflow_state == "Greeting" and current_step.step == "tts":
                    # For greeting TTS, set the greeting message in memory as llm_answer
                    greeting_text = "Hello, how can I assist you today?"
                    await self.memory_manager.set("contextual", "llm_answer", greeting_text)
                    input_data = { "text":greeting_text }  # Let the pipeline get text from memory
                elif workflow_state == "Goodbye" and current_step.step == "tts":
                    # For goodbye TTS, set the goodbye message in memory as llm_answer
                    goodbye_text = "Thank you for using our service. Goodbye!"
                    await self.memory_manager.set("contextual", "llm_answer", goodbye_text)
                    print(f"[DEBUG] Set goodbye text in memory: {goodbye_text}")
                    input_data = {"text":goodbye_text}  # Let the pipeline get text from memory
                elif current_step.step == "stt":
                    # For STT steps, retrieve audio path from memory manager
                    audio_path = await self.memory_manager.contextual.get("user_input_audio_path")
                    if not audio_path:
                        audio_path = await self.memory_manager.contextual.get("audio_path")
                    print(f"[DEBUG] STT step - checking for audio. self.memory_manager.get('audio_path')")
                    if audio_path:
                        input_data = {"audio_path": audio_path}
                        print(f"[DEBUG] Using audio path from memory: {audio_path}")
                    else:
                        # # Fallback to default if no audio path in memory
                        # input_data = {"audio_path": "fillerWords/user_conversation_part_1.mp3"}
                        # print(f"[DEBUG] No audio path in memory, using fallback")
                        print(f"[DEBUG] No audio path in memory - STT requires audio input")
                        self.logger.error("STT step requires audio_path in memory but none found", action="stt_input_validation", layer="orchestrator_v3")
                        return {'status': 'aborted', 'reason': 'STT step requires audio_path in memory but none found'}
                else:
                    # For all other steps, let StateManager handle input construction from memory
                    input_data = {}
                # Execute the pipeline step with retry logic
                retry_count = 0
                max_retries = 1
                while retry_count <= max_retries:
                    try:
                        if input_data is not None:
                            result = await self.state_manager.executePipelineState(input_data)
                            # input("Press Enter to continue...")
                        else:
                            result = await self.state_manager.executePipelineState()
                            # input("Press Enter to continue...")
                        await asyncio.sleep(0.1)
                        break
                    except Exception as e:
                        self.logger.error(f"Agent execution failed: {e}", action="executePipelineState", layer="orchestrator_v3")
                        retry_count += 1
                        if retry_count > max_retries:
                            self.logger.error(f"Agent failed after retry. Aborting.", action="agent_failure", layer="orchestrator_v3")
                            # ALERT: Could add alerting logic here (e.g., send notification)
                            return {'status': 'aborted', 'reason': f"Agent failed after retry: {e}"}
                output = None
                if isinstance(result, dict):
                    output = result.get('outputs') if 'outputs' in result else result.get('output') if 'output' in result else result
                elif hasattr(result, 'dict') and callable(getattr(result, 'dict')):
                    result_dict = result.dict()
                    output = result_dict.get('outputs') if 'outputs' in result_dict else result_dict.get('output') if 'output' in result_dict else result_dict
                elif hasattr(result, 'model_dump') and callable(getattr(result, 'model_dump')):
                    result_dict = result.model_dump()
                    output = result_dict.get('outputs') if 'outputs' in result_dict else result_dict.get('output') if 'output' in result_dict else result_dict
                else:
                    try:
                        output = dict(result)
                    except Exception as e:
                        self.logger.error(f"Agent output could not be converted to dict: {e}")
                        raise RuntimeError(f"Agent output could not be converted to dict: {e}")
                if output is None:
                    self.logger.warning(f"Agent output is None. Attempting to fetch from memory/context as fallback.")
                    output = {}
                    for out_key, mem_key in current_step.output.items():
                        mem_key_full = f"{self.session_id}_{await self.state_manager.getCurrentWorkflowState()}_{mem_key}"
                        value = await self.memory_manager.get(mem_key_full)
                        if value is not None:
                            output[out_key] = value
                    if not output:
                        self.logger.error(f"No output found in memory/context for step '{current_step.step}'. Aborting pipeline.")
                        raise RuntimeError(f"No output for step '{current_step.step}' in agent return or memory/context.")
                last_step_output = output
                if isinstance(output, dict):
                    for out_key, out_value in output.items():
                        await self.memory_manager.set("contextual", out_key, out_value)
                await self._track_conversation_turn(current_step.step, output)
                # Aggregate agent outputs and confidences for multi-agent support
                agent_outputs = []
                agent_confidences = []
                for agent_name in agent_names:
                    # Use the current step output instead of cache - format it nicely for LLM
                    if isinstance(output, dict):
                        # Format the output nicely for LLM evaluation
                        formatted_output = []
                        for key, value in output.items():
                            formatted_output.append(f"{key}: {value}")
                        agent_outputs.append(formatted_output)
                    else:
                        agent_outputs.append([str(output)])
                    conf_val = await self.get_agent_confidence(agent_name)
                    if conf_val is not None:
                        agent_confidences.append(conf_val)
                # Fetch user query and intent using correct memory key format
                workflow_state = await self.state_manager.getCurrentWorkflowState()
                user_query_key = f"{self.session_id}_{workflow_state}_clean_text"
                intent_key = f"{self.session_id}_{workflow_state}_intent"

                print(f"[DEBUG] Memory key lookup:")
                print(f"  Workflow State: {workflow_state}")
                print(f"  User Query Key: {user_query_key}")
                print(f"  Intent Key: {intent_key}")

                user_query = await self.memory_manager.get(user_query_key)
                intent = await self.memory_manager.get(intent_key)

                if user_query is None:
                    # Fallback to simple key
                    user_query = await self.memory_manager.get("clean_text")
                if intent is None:
                    # Fallback to simple key
                    intent = await self.memory_manager.get("intent")

                print(f"[DEBUG] Retrieved from memory:")
                print(f"  User Query (clean_text): {user_query}")
                print(f"  Intent: {intent}")
                print(f"  Agent Output: {output}")

                # Debug: Show all contextual memory keys
                all_contextual = await self.memory_manager.get_all_contextual()
                print(f"[DEBUG] All contextual memory keys: {list(all_contextual.keys())}")
                # Only evaluate after preprocessing/processing steps
                if current_step.step in ["preprocessing", "processing"]:
                    # If any confidence > 90, proceed
                    if any([c for c in agent_confidences if isinstance(c, (int, float)) and c > 0.9]):
                        next_step_id = None
                        for idx, step in enumerate(current_pipeline):
                            if hasattr(step, "step") and step.step == pipeline_state:
                                if idx + 1 < len(current_pipeline):
                                    next_step_id = current_pipeline[idx + 1].step
                                break
                        if next_step_id:
                            await self.state_manager.transitionPipeline(next_step_id)
                            continue
                        else:
                            # End of pipeline, handle workflow transition
                            workflow = await self.state_manager.getWorkflow()
                            workflow_obj = workflow.workflow
                            state_config = workflow_obj.states.get(workflow_state)

                            # Check if this is an end state with no transitions
                            if state_config and state_config.type == "end" and (not state_config.transitions or len(state_config.transitions) == 0):
                                if self.continuous_mode:
                                    # In continuous mode, end states (like Goodbye) should stop conversation
                                    self.conversation_active = False
                                    self.status = "completed"
                                    self.reason = f"Conversation ended at state: {workflow_state}"
                                    self.logger.info(f"Conversation ended at state: {workflow_state}", action="conversation_end", layer="orchestrator_v3")
                                    return {"status": self.status, "reason": self.reason, "key_events": self.key_events}
                                else:
                                    self.status = "completed"
                                    self.reason = f"Workflow completed successfully at end state: {workflow_state}"
                                    self.logger.info(f"Workflow completed at end state: {workflow_state}", action="workflow_complete", layer="orchestrator_v3")
                                    return {"status": self.status, "reason": self.reason, "key_events": self.key_events}

                            allowed_transitions = [t.target for t in state_config.transitions] if state_config and state_config.transitions else []
                            if not allowed_transitions:
                                # No transitions available, check if Goodbye state exists and auto-transition
                                workflow = await self.state_manager.getWorkflow()
                                workflow_obj = workflow.workflow
                                print(f"[DEBUG] No transitions from {workflow_state}. Available states: {list(workflow_obj.states.keys())}")
                                if "Goodbye" in workflow_obj.states:
                                    print(f"[DEBUG] No transitions available from {workflow_state}, auto-transitioning to Goodbye")
                                    await self.state_manager.transitionWorkflow("Goodbye")
                                    continue
                                else:
                                    # No Goodbye state, complete the workflow
                                    print(f"[DEBUG] No Goodbye state found, completing workflow")
                                    self.status = "completed"
                                    self.reason = f"Workflow completed - no transitions available from state: {workflow_state}"
                                    self.logger.info(f"Workflow completed - no transitions from: {workflow_state}", action="workflow_complete", layer="orchestrator_v3")
                                    return {"status": self.status, "reason": self.reason, "key_events": self.key_events}
                            else:
                                # LLM prompt for workflow transition
                                llm_prompt = f"""
You are an AI orchestrator deciding which workflow state to transition to next in a banking conversation.\n
Context:\n- User Query: {user_query}\n- Detected Intent: {intent}\n- Allowed Transitions: {allowed_transitions}\n
Instructions:\n- Choose the most appropriate next state from the allowed transitions, based on the user query and detected intent.\n- Respond with ONLY the name of the next state from the allowed transitions.\n- If you are unsure, or if the allowed transitions list is empty, respond with \"Goodbye\".\n"""
                                decision = await self.evaluate_llm_workflow_transition(llm_prompt, allowed_transitions)
                                if decision not in allowed_transitions:
                                    next_workflow_state = "Goodbye"
                                else:
                                    next_workflow_state = decision
                                await self.state_manager.transitionWorkflow(next_workflow_state)
                                continue
                    else:
                        # LLM evaluation based only on intent vs user query match (no confidence values)
                        print(f"[DEBUG] Low confidence, using LLM evaluation based on intent match")
                        llm_prompt = f"""
You are an AI orchestrator evaluating whether to proceed to the next pipeline step or redo the current step.

Context:
- Pipeline Step: {current_step.step}
- User Query: {user_query}
- Detected Intent: {intent}

Instructions:
- Evaluate ONLY if the detected intent correctly matches what the user is asking for
- For preprocessing steps: If the user asks about "balance" and intent is "account_balance" → respond "proceed"
- For preprocessing steps: If the user asks about "transfer" and intent is "fund_transfer" → respond "proceed"
- For preprocessing steps: If the user asks about "payment" and intent is "payment" → respond "proceed"
- For preprocessing steps: If the user asks about "exchange" and intent is "exchange_rate" → respond "proceed"
- For preprocessing steps: If the user asks about "goodbye" and intent is "goodbye" → respond "proceed"
- Only respond "redo" if the intent is completely wrong or missing
- Respond with ONLY one word: "proceed" or "redo"

Analysis:
- User Query: "{user_query}"
- Detected Intent: "{intent}"
- Does the intent match the user's request? If YES → "proceed", if NO → "redo"
"""
                        print(f"[DEBUG] LLM Evaluation Data:")
                        print(f"  Pipeline Step: {current_step.step}")
                        print(f"  User Query: {user_query}")
                        print(f"  Agent Outputs: {agent_outputs}")
                        print(f"  Agent Confidences: {agent_confidences}")
                        print(f"  LLM Prompt: {llm_prompt}")
                        decision = await self.evaluate_llm_pipeline_transition(llm_prompt)
                        if decision == "proceed":
                            next_step_id = None
                            for idx, step in enumerate(current_pipeline):
                                if hasattr(step, "step") and step.step == pipeline_state:
                                    if idx + 1 < len(current_pipeline):
                                        next_step_id = current_pipeline[idx + 1].step
                                    break
                            if next_step_id:
                                await self.state_manager.transitionPipeline(next_step_id)
                                continue
                            else:
                                # End of pipeline, handle workflow transition
                                workflow = await self.state_manager.getWorkflow()
                                workflow_obj = workflow.workflow
                                state_config = workflow_obj.states.get(workflow_state)

                                # Check if this is an end state with no transitions
                                if state_config and state_config.type == "end" and (not state_config.transitions or len(state_config.transitions) == 0):
                                    self.status = "completed"
                                    self.reason = f"Workflow completed successfully at end state: {workflow_state}"
                                    self.logger.info(f"Workflow completed at end state: {workflow_state}", action="workflow_complete", layer="orchestrator_v3")
                                    return {"status": self.status, "reason": self.reason, "key_events": self.key_events}

                                allowed_transitions = [t.target for t in state_config.transitions] if state_config and state_config.transitions else []
                                if not allowed_transitions:
                                    # No transitions available, check if Goodbye state exists and auto-transition
                                    workflow = await self.state_manager.getWorkflow()
                                    workflow_obj = workflow.workflow
                                    print(f"[DEBUG] No transitions from {workflow_state}. Available states: {list(workflow_obj.states.keys())}")
                                    if "Goodbye" in workflow_obj.states:
                                        print(f"[DEBUG] No transitions available from {workflow_state}, auto-transitioning to Goodbye")
                                        await self.state_manager.transitionWorkflow("Goodbye")
                                        continue
                                    else:
                                        # No Goodbye state, complete the workflow
                                        print(f"[DEBUG] No Goodbye state found, completing workflow")
                                        self.status = "completed"
                                        self.reason = f"Workflow completed - no transitions available from state: {workflow_state}"
                                        self.logger.info(f"Workflow completed - no transitions from: {workflow_state}", action="workflow_complete", layer="orchestrator_v3")
                                        return {"status": self.status, "reason": self.reason, "key_events": self.key_events}
                                else:
                                    llm_prompt = f"""
You are an AI orchestrator deciding which workflow state to transition to next in a banking conversation.\n
Context:\n- User Query: {user_query}\n- Detected Intent: {intent}\n- Allowed Transitions: {allowed_transitions}\n
Instructions:\n- Choose the most appropriate next state from the allowed transitions, based on the user query and detected intent.\n- Respond with ONLY the name of the next state from the allowed transitions.\n- If you are unsure, or if the allowed transitions list is empty, respond with \"Goodbye\".\n"""
                                    decision = await self.evaluate_llm_workflow_transition(llm_prompt, allowed_transitions)
                                    if decision not in allowed_transitions:
                                        next_workflow_state = "Goodbye"
                                    else:
                                        next_workflow_state = decision
                                    await self.state_manager.transitionWorkflow(next_workflow_state)
                                    continue
                        else:
                            self.logger.info(f"LLM requested redo for step {current_step.step}. Aborting after one retry.")
                            return {'status': 'aborted', 'reason': f'LLM requested redo for step {current_step.step}'}
                else:
                    # For other steps, always proceed to next pipeline step
                    next_step_id = None
                    for idx, step in enumerate(current_pipeline):
                        if hasattr(step, "step") and step.step == pipeline_state:
                            if idx + 1 < len(current_pipeline):
                                next_step_id = current_pipeline[idx + 1].step
                            break
                    if next_step_id:
                        await self.state_manager.transitionPipeline(next_step_id)
                        continue
                    else:
                        # End of pipeline, handle workflow transition
                        workflow = await self.state_manager.getWorkflow()
                        workflow_obj = workflow.workflow
                        state_config = workflow_obj.states.get(workflow_state)

                        # Check if this is an end state with no transitions
                        if state_config and state_config.type == "end" and (not state_config.transitions or len(state_config.transitions) == 0):
                            self.status = "completed"
                            self.reason = f"Workflow completed successfully at end state: {workflow_state}"
                            self.logger.info(f"Workflow completed at end state: {workflow_state}", action="workflow_complete", layer="orchestrator_v3")
                            return {"status": self.status, "reason": self.reason, "key_events": self.key_events}

                        allowed_transitions = [t.target for t in state_config.transitions] if state_config and state_config.transitions else []
                        if not allowed_transitions:
                            # No transitions available, check if Goodbye state exists and auto-transition
                            workflow = await self.state_manager.getWorkflow()
                            workflow_obj = workflow.workflow
                            print(f"[DEBUG] No transitions from {workflow_state}. Available states: {list(workflow_obj.states.keys())}")
                            if "Goodbye" in workflow_obj.states:
                                print(f"[DEBUG] No transitions available from {workflow_state}, auto-transitioning to Goodbye")
                                await self.state_manager.transitionWorkflow("Goodbye")
                                continue
                            else:
                                # No Goodbye state, complete the workflow
                                print(f"[DEBUG] No Goodbye state found, completing workflow")
                                self.status = "completed"
                                self.reason = f"Workflow completed - no transitions available from state: {workflow_state}"
                                self.logger.info(f"Workflow completed - no transitions from: {workflow_state}", action="workflow_complete", layer="orchestrator_v3")
                                return {"status": self.status, "reason": self.reason, "key_events": self.key_events}
                        else:
                            llm_prompt = f"""
You are an AI orchestrator deciding which workflow state to transition to next in a banking conversation.\n
Context:\n- User Query: {user_query}\n- Detected Intent: {intent}\n- Allowed Transitions: {allowed_transitions}\n
Instructions:\n- Choose the most appropriate next state from the allowed transitions, based on the user query and detected intent.\n- Respond with ONLY the name of the next state from the allowed transitions.\n- If you are unsure, or if the allowed transitions list is empty, respond with \"Goodbye\".\n"""
                            decision = await self.evaluate_llm_workflow_transition(llm_prompt, allowed_transitions)
                            if decision not in allowed_transitions:
                                next_workflow_state = "Goodbye"
                            else:
                                next_workflow_state = decision
                            await self.state_manager.transitionWorkflow(next_workflow_state)
                            continue
        except Exception as e:
            self.status = "aborted"
            self.reason = f"Exception in orchestrator run: {str(e)}"
            self.logger.error(self.reason, action="run", layer="orchestrator_v3")
            return {"status": self.status, "reason": self.reason, "key_events": [self.reason]}
        return {"status": self.status, "reason": self.reason, "key_events": self.key_events}

    async def evaluate_llm_pipeline_transition(self, prompt):
        try:
            client = openai.AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))
            response = await client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=10,
                temperature=0.1
            )
            decision = response.choices[0].message.content.strip().lower()
            return "proceed" if "proceed" in decision else "redo"
        except Exception as e:
            self.logger.error(f"LLM evaluation failed: {e}", action="evaluate_llm_pipeline_transition", layer="orchestrator_v3")
            return "redo"

    async def evaluate_llm_workflow_transition(self, prompt, allowed_transitions):
        try:
            client = openai.AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))
            response = await client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=10,
                temperature=0.1
            )
            decision = response.choices[0].message.content.strip()
            if decision in allowed_transitions:
                return decision
            return "Goodbye"
        except Exception as e:
            self.logger.error(f"LLM workflow transition evaluation failed: {e}", action="evaluate_llm_workflow_transition", layer="orchestrator_v3")
            return "Goodbye"

    async def _track_conversation_turn(self, step_name: str, output: dict):
        """Track conversation turns for dialog logging"""
        try:
            # Capture user message from STT step
            if step_name == "stt_process" and isinstance(output, dict):
                if "text" in output:
                    self.user_message = output["text"]
                    print(f"[DEBUG] Captured user message: {self.user_message}")

            # Capture AI response from TTS step and save conversation turn
            elif step_name == "tts" and isinstance(output, dict):
                if "greeting_response" in output:
                    self.ai_response = output["greeting_response"]
                    print(f"[DEBUG] Captured AI response: {self.ai_response}")

                    # Save the complete conversation turn
                    if self.user_message and self.ai_response:
                        # Get intent from memory
                        intent = await self.memory_manager.get("intent") or "unknown"
                        await self.memory_manager.save_conversation_turn(
                            user_message=self.user_message,
                            ai_message=self.ai_response,
                            intent=intent
                        )
                        print(f"[DEBUG] Saved conversation turn: user='{self.user_message}' ai='{self.ai_response}' intent='{intent}'")

                        # Reset for next turn
                        self.user_message = ""
                        self.ai_response = ""
        except Exception as e:
            print(f"[DEBUG] Error tracking conversation turn: {e}")

    async def cleanup(self):
        # Stub for orchestrator cleanup logic
        pass